# 📦 Core Libraries and CPU Optimization Setup
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import gc
import psutil
import os
import time
from datetime import datetime

# PyTorch and Lightning
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping, LearningRateMonitor

# ML Libraries
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix, f1_score, precision_score, recall_score

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

print("📦 Libraries imported successfully")

# 🔧 Advanced CPU Optimization Setup
def setup_cpu_optimization():
    """Configure optimal CPU settings for maximum performance"""
    print("🔧 Setting up advanced CPU optimizations...")
    
    # Get system information
    cpu_cores = psutil.cpu_count(logical=False)
    logical_cores = psutil.cpu_count(logical=True)
    available_ram = psutil.virtual_memory().total / (1024**3)
    
    # Configure optimal thread counts
    optimal_threads = min(cpu_cores, 8)  # Cap at 8 for stability
    
    # Set environment variables for CPU optimization
    os.environ['OMP_NUM_THREADS'] = str(optimal_threads)
    os.environ['MKL_NUM_THREADS'] = str(optimal_threads)
    os.environ['NUMEXPR_NUM_THREADS'] = str(optimal_threads)
    os.environ['OPENBLAS_NUM_THREADS'] = str(optimal_threads)
    
    # Configure PyTorch for CPU optimization
    torch.set_num_threads(optimal_threads)
    torch.set_num_interop_threads(min(cpu_cores, 4))
    
    # Enable CPU optimizations
    if not torch.cuda.is_available():
        torch.backends.mkldnn.enabled = True  # Enable Intel MKL-DNN
        print("🔧 CPU-only mode: MKL-DNN enabled")
    
    print(f"💻 System Configuration:")
    print(f"   Physical cores: {cpu_cores}")
    print(f"   Logical cores: {logical_cores}")
    print(f"   Optimal threads: {optimal_threads}")
    print(f"   Available RAM: {available_ram:.1f} GB")
    print(f"   PyTorch threads: {torch.get_num_threads()}")
    print(f"   Interop threads: {torch.get_num_interop_threads()}")
    print(f"   CUDA available: {torch.cuda.is_available()}")
    
    return {
        'cpu_cores': cpu_cores,
        'optimal_threads': optimal_threads,
        'available_ram': available_ram,
        'is_gpu_available': torch.cuda.is_available()
    }

def monitor_system_resources():
    """Monitor current system resource usage"""
    memory = psutil.virtual_memory()
    cpu_percent = psutil.cpu_percent(interval=1)
    
    print(f"🖥️ System Resources:")
    print(f"   CPU Usage: {cpu_percent:.1f}%")
    print(f"   Memory Usage: {memory.percent:.1f}%")
    print(f"   Available RAM: {memory.available / (1024**3):.1f} GB")
    print(f"   Used RAM: {memory.used / (1024**3):.1f} GB")
    
    return {
        'cpu_percent': cpu_percent,
        'memory_percent': memory.percent,
        'available_gb': memory.available / (1024**3),
        'used_gb': memory.used / (1024**3)
    }

def optimize_memory_usage():
    """Optimize memory usage and clean up"""
    print("🧹 Optimizing memory usage...")
    
    # Force garbage collection
    gc.collect()
    
    # Clear PyTorch cache if using GPU
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # Get memory info after cleanup
    memory = psutil.virtual_memory()
    print(f"💾 Memory after optimization:")
    print(f"   Available: {memory.available / (1024**3):.1f} GB")
    print(f"   Usage: {memory.percent:.1f}%")

# Apply CPU optimizations
system_info = setup_cpu_optimization()
initial_resources = monitor_system_resources()
optimize_memory_usage()

print("\n✅ CPU optimization setup complete!")

# 📊 Load and Prepare Data
print("📊 Loading CELEST AI training data...")

# Load the dataset
try:
    # Try to load from Kaggle dataset
    # Try multiple possible paths for the Kaggle dataset
    # Try multiple possible paths for the CELEST AI dataset (Parquet and CSV)
    data_paths = [
        # Parquet files (primary format)
        "training_data_2010_2011.parquet",                     # Current directory
        "data/training_data_2010_2011.parquet",                # Local data folder
        "/kaggle/input/hackthon/training_data_2010_2011.parquet", # Kaggle path
        "/kaggle/input/dhananjaypriyadarshi-hackthon/training_data_2010_2011.parquet", # Updated Kaggle
        "../input/hackthon/training_data_2010_2011.parquet",   # Relative Kaggle
        
        # CSV files (fallback)
        "/kaggle/input/hackthon/train.csv",                    # Original CSV path
        "/kaggle/input/dhananjaypriyadarshi-hackthon/train.csv", # Updated CSV path
        "../input/hackthon/train.csv",                         # Relative CSV path
        "data/train.csv",                                      # Local CSV path
        "train.csv"                                            # Current directory CSV
    ]
    
    data_path = None
    for path in data_paths:
        if os.path.exists(path):
            data_path = path
            print(f"📂 Found dataset at: {data_path}")
            break
    
    if data_path is None:
        raise FileNotFoundError("Dataset not found in any expected location")
    
    # Load data based on file extension
    if data_path.endswith('.parquet'):
        df = pd.read_parquet(data_path)
        print(f"✅ Real CELEST AI data loaded from Parquet: {df.shape}")
    else:
        df = pd.read_csv(data_path)
        print(f"✅ Real CELEST AI data loaded from CSV: {df.shape}")
    
    print(f"📊 Dataset columns: {list(df.columns)}")
    print(f"🎉 Using authentic solar wind data for CME detection!")
    print(f"📅 Data timespan: 2010-2011 solar wind measurements")
    
except FileNotFoundError:
    print("⚠️ Dataset not found. Creating synthetic data for demonstration...")
    
    # Create synthetic data that mimics real solar wind data with CME events
    np.random.seed(42)
    n_samples = 50000  # Reduced for faster processing
    
    # Generate baseline solar wind parameters with proper scaling
    df = pd.DataFrame({
        'bx_gsm': np.random.normal(0, 3, n_samples),
        'by_gsm': np.random.normal(0, 3, n_samples), 
        'bz_gsm': np.random.normal(-1, 4, n_samples),
        'bt': np.random.lognormal(1.5, 0.5, n_samples),
        'density': np.random.lognormal(1.0, 0.8, n_samples),
        'speed': np.random.normal(400, 100, n_samples),
        'temperature': np.random.lognormal(11.5, 0.8, n_samples)  # Better temperature distribution
    })
    
    # Inject CME-like events (about 5% of data) to ensure we have both classes
    n_cme_events = int(0.05 * n_samples)
    cme_indices = np.random.choice(n_samples, n_cme_events, replace=False)
    
    # Create CME-like conditions
    df.loc[cme_indices, 'bz_gsm'] = np.random.normal(-8, 3, n_cme_events)  # Strong southward Bz
    df.loc[cme_indices, 'bt'] = np.random.normal(15, 5, n_cme_events)      # Enhanced magnetic field
    df.loc[cme_indices, 'speed'] = np.random.normal(550, 80, n_cme_events) # High speed
    df.loc[cme_indices, 'density'] = np.random.normal(8, 3, n_cme_events)  # Enhanced density
    
    # Ensure realistic ranges
    df['speed'] = np.clip(df['speed'], 250, 800)
    df['density'] = np.clip(df['density'], 0.1, 50)
    df['bt'] = np.clip(df['bt'], 1, 50)
    df['bz_gsm'] = np.clip(df['bz_gsm'], -30, 15)
    df['temperature'] = np.clip(df['temperature'], 50000, 2000000)  # More realistic temperature range
    
    print(f"✅ Synthetic data created: {df.shape}")

# Display basic information
print(f"\n📈 Dataset Overview:")
print(f"   Shape: {df.shape}")
print(f"   Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.1f} MB")
print(f"   Columns: {list(df.columns)}")

# Check for missing values
missing_values = df.isnull().sum()
if missing_values.sum() > 0:
    print(f"\n⚠️ Missing values detected:")
    print(missing_values[missing_values > 0])
else:
    print("\n✅ No missing values detected")

# Display first few rows
print("\n📋 First 5 rows:")
display(df.head())

# Monitor memory after data loading
print("\n📊 Memory usage after data loading:")
monitor_system_resources()

# 🧪 Physics-Driven Consensus Engine (PDCE) - Enhanced Version
class EnhancedPDCE:
    """Enhanced Physics-Driven Consensus Engine for CME detection"""
    
    def __init__(self, 
                 bz_threshold=-5.0,
                 bt_threshold=10.0, 
                 speed_threshold=450.0,
                 density_threshold=5.0,
                 min_duration_minutes=45,
                 lookhead_minutes=45):
        
        self.bz_threshold = bz_threshold
        self.bt_threshold = bt_threshold
        self.speed_threshold = speed_threshold
        self.density_threshold = density_threshold
        self.min_duration_minutes = min_duration_minutes
        self.lookhead_minutes = lookhead_minutes
        
        print(f"🧪 Enhanced PDCE initialized with thresholds:")
        print(f"   Bz < {bz_threshold} nT")
        print(f"   Bt > {bt_threshold} nT")
        print(f"   Speed > {speed_threshold} km/s")
        print(f"   Density > {density_threshold} cm⁻³")
        print(f"   Min duration: {min_duration_minutes} minutes")
        print(f"   Lookhead: {lookhead_minutes} minutes")
    
    def detect_cme_conditions(self, df):
        """Detect individual CME conditions"""
        conditions = pd.DataFrame(index=df.index)
        
        # Primary conditions
        conditions['bz_south'] = df['bz_gsm'] < self.bz_threshold
        conditions['bt_enhanced'] = df['bt'] > self.bt_threshold
        conditions['speed_high'] = df['speed'] > self.speed_threshold
        conditions['density_enhanced'] = df['density'] > self.density_threshold
        
        # Secondary physics-based conditions
        conditions['dynamic_pressure'] = (1.67e-6 * df['density'] * df['speed']**2) > 2.0  # nPa
        conditions['bz_persistence'] = df['bz_gsm'].rolling(window=3, center=True).mean() < self.bz_threshold
        
        return conditions
    
    def apply_consensus_logic(self, conditions):
        """Apply multi-condition consensus for CME detection"""
        # Primary consensus: At least 3 of 4 main conditions
        primary_score = (
            conditions['bz_south'].astype(int) +
            conditions['bt_enhanced'].astype(int) +
            conditions['speed_high'].astype(int) +
            conditions['density_enhanced'].astype(int)
        )
        
        # Secondary consensus: Additional physics validation
        secondary_score = (
            conditions['dynamic_pressure'].astype(int) +
            conditions['bz_persistence'].astype(int)
        )
        
        # Combined consensus: Primary >= 3 OR (Primary >= 2 AND Secondary >= 1)
        consensus = (primary_score >= 3) | ((primary_score >= 2) & (secondary_score >= 1))
        
        return consensus, primary_score, secondary_score
    
    def apply_temporal_filtering(self, consensus):
        """Apply temporal filtering to remove short-duration events"""
        # Convert to binary array
        events = consensus.astype(int)
        
        # Find event boundaries
        event_starts = np.where(np.diff(np.concatenate(([0], events))) == 1)[0]
        event_ends = np.where(np.diff(np.concatenate((events, [0]))) == -1)[0]
        
        # Filter events by minimum duration
        filtered_events = np.zeros_like(events)
        
        for start, end in zip(event_starts, event_ends):
            duration = end - start + 1
            if duration >= self.min_duration_minutes:
                filtered_events[start:end+1] = 1
        
        return pd.Series(filtered_events, index=consensus.index, dtype=bool)
    
    def generate_labels(self, df):
        """Generate enhanced CME labels with predictive lookhead"""
        print("🔬 Generating enhanced CME labels...")
        
        # Detect conditions
        conditions = self.detect_cme_conditions(df)
        
        # Apply consensus logic
        consensus, primary_score, secondary_score = self.apply_consensus_logic(conditions)
        
        # Apply temporal filtering
        filtered_events = self.apply_temporal_filtering(consensus)
        
        # Apply predictive lookhead
        predictive_labels = np.zeros(len(df), dtype=int)
        
        for i in range(len(df) - self.lookhead_minutes):
            # Look ahead for CME events
            future_window = filtered_events.iloc[i:i+self.lookhead_minutes]
            if future_window.any():
                predictive_labels[i] = 1
        
        # Create result dataframe
        result_df = df.copy()
        result_df['event_label'] = predictive_labels
        result_df['consensus_score'] = primary_score + secondary_score
        result_df['primary_score'] = primary_score
        result_df['secondary_score'] = secondary_score
        
        # Calculate statistics
        total_events = np.sum(predictive_labels)
        event_rate = total_events / len(df) * 100
        
        print(f"✅ Label generation complete:")
        print(f"   Total samples: {len(df):,}")
        print(f"   CME events detected: {total_events:,}")
        print(f"   Event rate: {event_rate:.2f}%")
        print(f"   Class balance: {100-event_rate:.1f}% Normal, {event_rate:.1f}% CME")
        
        return result_df

# Initialize and apply Enhanced PDCE
pdce = EnhancedPDCE()
labeled_df = pdce.generate_labels(df)

print("\n📊 Memory usage after labeling:")
monitor_system_resources()

# 🔧 Advanced Feature Engineering with CPU Optimization
def create_enhanced_features(df):
    """Create physics-informed features optimized for CPU processing"""
    print("🔧 Creating enhanced physics-informed features...")
    
    # Work with float32 for memory efficiency
    data = df.copy().astype(np.float32)
    
    # Magnetic field features
    data['b_total'] = np.sqrt(data['bx_gsm']**2 + data['by_gsm']**2 + data['bz_gsm']**2)
    data['bz_bt_ratio'] = data['bz_gsm'] / (data['bt'] + 1e-6)
    data['b_magnitude'] = data['bt']
    
    # Solar wind dynamics
    data['dynamic_pressure'] = 1.67e-6 * data['density'] * data['speed']**2  # nPa
    data['kinetic_energy'] = 0.5 * data['density'] * data['speed']**2
    
    # Plasma physics parameters
    thermal_pressure = 1.38e-23 * data['density'] * 1e6 * data['temperature'] * 1e9  # nPa
    magnetic_pressure = data['bt']**2 / (2 * 4e-7 * np.pi) * 1e9  # nPa
    data['plasma_beta'] = thermal_pressure / (magnetic_pressure + 1e-6)
    
    # Alfvén speed (km/s)
    data['alfven_speed'] = data['bt'] * 1e-9 / np.sqrt(4e-7 * np.pi * data['density'] * 1e6 * 1.67e-27) / 1000
    
    # Temporal features (optimized with rolling windows)
    window_sizes = [3, 5, 10]
    
    for window in window_sizes:
        # Rolling statistics
        data[f'bz_mean_{window}'] = data['bz_gsm'].rolling(window=window, center=True).mean()
        data[f'speed_mean_{window}'] = data['speed'].rolling(window=window, center=True).mean()
        data[f'bt_std_{window}'] = data['bt'].rolling(window=window, center=True).std()
        
        # Gradients
        data[f'bz_gradient_{window}'] = data['bz_gsm'].rolling(window=window).apply(lambda x: np.gradient(x)[-1] if len(x) == window else 0)
        data[f'speed_gradient_{window}'] = data['speed'].rolling(window=window).apply(lambda x: np.gradient(x)[-1] if len(x) == window else 0)
    
    # Geoeffectiveness proxies
    data['geo_effectiveness'] = np.where(
        (data['bz_gsm'] < -5) & (data['speed'] > 400) & (data['dynamic_pressure'] > 2),
        1, 0
    )
    
    # Interaction terms
    data['bz_speed_interaction'] = data['bz_gsm'] * data['speed']
    data['bt_density_interaction'] = data['bt'] * data['density']
    
    # Fill NaN values with forward fill then backward fill
    data = data.ffill().bfill()
    
    print(f"✅ Feature engineering complete:")
    print(f"   Original features: {len(df.columns)}")
    print(f"   Enhanced features: {len(data.columns)}")
    print(f"   Memory usage: {data.memory_usage(deep=True).sum() / 1024**2:.1f} MB")
    
    return data

# Apply feature engineering
enhanced_df = create_enhanced_features(labeled_df)

# Define feature columns (exclude target and metadata)
exclude_cols = ['event_label', 'consensus_score', 'primary_score', 'secondary_score']
feature_columns = [col for col in enhanced_df.columns if col not in exclude_cols]

print(f"\n📊 Feature columns ({len(feature_columns)}):")
for i, col in enumerate(feature_columns):
    if i < 10:  # Show first 10
        print(f"   {col}")
    elif i == 10:
        print(f"   ... and {len(feature_columns)-10} more")
        break

print("\n📊 Memory usage after feature engineering:")
monitor_system_resources()

# 📊 CPU-Optimized Dataset Class
class CPUOptimizedCMEDataset(Dataset):
    """Memory and CPU optimized PyTorch Dataset for time-series data"""
    
    def __init__(self, df, feature_columns, target_column='event_label', sequence_length=180):
        self.sequence_length = sequence_length
        self.n_features = len(feature_columns)
        
        print(f"📊 Optimizing dataset for {len(df):,} samples...")
        
        # Memory optimization: Convert to optimal data types
        features_df = df[feature_columns].copy()
        
        # Use float32 instead of float64 to halve memory usage
        for col in feature_columns:
            if features_df[col].dtype == 'float64':
                features_df[col] = features_df[col].astype(np.float32)
        
        # Store as contiguous arrays for better CPU cache performance
        self.features = np.ascontiguousarray(features_df.values, dtype=np.float32)
        self.targets = np.ascontiguousarray(df[target_column].values, dtype=np.int64)
        
        # Pre-allocate tensor for reuse (reduces memory allocation overhead)
        self._temp_sequence = np.empty((sequence_length, self.n_features), dtype=np.float32)
        
        # Memory usage reporting
        features_mb = self.features.nbytes / (1024 * 1024)
        targets_mb = self.targets.nbytes / (1024 * 1024)
        total_mb = features_mb + targets_mb
        
        print(f"💾 Dataset memory usage:")
        print(f"   Features: {features_mb:.1f} MB")
        print(f"   Targets: {targets_mb:.1f} MB")
        print(f"   Total: {total_mb:.1f} MB")
        print(f"   Sequences available: {len(self):,}")
        
        # Clean up temporary data
        del features_df
        gc.collect()

    def __len__(self):
        return len(self.features) - self.sequence_length + 1

    def __getitem__(self, idx):
        # Optimized sequence extraction
        end_idx = idx + self.sequence_length
        
        # Use pre-allocated array to avoid repeated allocations
        np.copyto(self._temp_sequence, self.features[idx:end_idx])
        
        # Convert to tensor efficiently
        sequence_tensor = torch.from_numpy(self._temp_sequence.copy())
        target_tensor = torch.tensor(self.targets[end_idx - 1], dtype=torch.long)
        
        return sequence_tensor, target_tensor
    
    def get_memory_usage_mb(self):
        """Get current memory usage in MB"""
        return (self.features.nbytes + self.targets.nbytes) / (1024 * 1024)

print("✅ CPU-optimized dataset class created")

# 🔄 Data Splitting and Preprocessing
print("🔄 Splitting data with temporal validation...")

# Chronological split to prevent data leakage
split_idx_1 = int(0.7 * len(enhanced_df))
split_idx_2 = int(0.85 * len(enhanced_df))

train_df = enhanced_df.iloc[:split_idx_1].copy()
val_df = enhanced_df.iloc[split_idx_1:split_idx_2].copy()
test_df = enhanced_df.iloc[split_idx_2:].copy()

print(f"📊 Data split:")
print(f"   Train: {len(train_df):,} samples ({len(train_df)/len(enhanced_df)*100:.1f}%)")
print(f"   Validation: {len(val_df):,} samples ({len(val_df)/len(enhanced_df)*100:.1f}%)")
print(f"   Test: {len(test_df):,} samples ({len(test_df)/len(enhanced_df)*100:.1f}%)")

# Check class distribution
for name, df_split in [('Train', train_df), ('Validation', val_df), ('Test', test_df)]:
    event_rate = df_split['event_label'].mean() * 100
    print(f"   {name} event rate: {event_rate:.2f}%")

# Feature scaling with CPU optimization
print("\n🔧 Applying feature scaling...")

scaler = StandardScaler()

# Fit on training data only
train_df[feature_columns] = scaler.fit_transform(train_df[feature_columns].astype(np.float32))
val_df[feature_columns] = scaler.transform(val_df[feature_columns].astype(np.float32))
test_df[feature_columns] = scaler.transform(test_df[feature_columns].astype(np.float32))

print("✅ Feature scaling complete")

# Configuration with CPU optimization
def get_optimized_config():
    """Get configuration optimized for current hardware"""
    
    # Adjust batch size based on available RAM
    available_ram = system_info['available_ram']
    
    if available_ram < 8:
        batch_size = 32
        print("📊 Low RAM detected: Using batch size 32")
    elif available_ram < 16:
        batch_size = 64
        print("📊 Standard RAM: Using batch size 64")
    else:
        batch_size = 128
        print("📊 High RAM detected: Using batch size 128")
    
    config = {
        # Model architecture
        'sequence_length': 180,  # 3 hours of data
        'patch_size': 12,        # 12-minute patches
        'd_model': 128,          # Model dimension
        'n_heads': 8,            # Attention heads
        'n_layers': 6,           # Transformer layers
        
        # Training parameters - CPU optimized
        'batch_size': batch_size,
        'learning_rate': 3e-5,   # Conservative for stability
        'max_epochs': 25,        # Reduced to prevent overfitting
        
        # Regularization
        'dropout': 0.25,         # Balanced regularization
        'label_smoothing': 0.1,  # Prevent overconfident predictions
        'gradient_clip_val': 0.5,
        
        # Loss function
        'use_focal_loss': True,
        'focal_loss_gamma': 1.5,
        
        # Early stopping
        'early_stopping_patience': 4,
        'lr_scheduler_patience': 2,
        
        # CPU optimization
        'num_workers': min(system_info['cpu_cores'], 4),
        'pin_memory': system_info['is_gpu_available'],
        'persistent_workers': True,
    }
    
    return config

CONFIG = get_optimized_config()

print(f"\n🔧 Optimized configuration:")
for key, value in CONFIG.items():
    print(f"   {key}: {value}")

print("\n📊 Memory usage after preprocessing:")
monitor_system_resources()

# 🤖 Enhanced PatchTST Model with Advanced Regularization
class EnhancedFocalLoss(nn.Module):
    """Focal Loss with label smoothing for better generalization"""
    def __init__(self, alpha=None, gamma=2.0, label_smoothing=0.1):
        super(EnhancedFocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.label_smoothing = label_smoothing

    def forward(self, inputs, targets):
        # Apply label smoothing
        if self.label_smoothing > 0:
            num_classes = inputs.size(1)
            targets_one_hot = torch.zeros_like(inputs)
            targets_one_hot.scatter_(1, targets.unsqueeze(1), 1)
            targets_one_hot = targets_one_hot * (1 - self.label_smoothing) + \
                             self.label_smoothing / num_classes
            ce_loss = -(targets_one_hot * torch.log_softmax(inputs, dim=1)).sum(dim=1)
        else:
            ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        
        pt = torch.exp(-ce_loss)
        if self.alpha is not None:
            alpha_t = self.alpha[targets] if self.label_smoothing == 0 else self.alpha.mean()
            focal_loss = alpha_t * (1 - pt)**self.gamma * ce_loss
        else:
            focal_loss = (1 - pt)**self.gamma * ce_loss
        
        return focal_loss.mean()

class EnhancedPatchTSTModel(pl.LightningModule):
    """Enhanced PatchTST with advanced regularization and CPU optimization"""
    
    def __init__(self, config, n_features, class_weights):
        super().__init__()
        self.save_hyperparameters()

        # Patching logic
        self.n_patches = config['sequence_length'] // config['patch_size']
        self.patch_embedding = nn.Linear(config['patch_size'] * n_features, config['d_model'])
        
        # Enhanced dropout strategies
        self.patch_dropout = nn.Dropout(config['dropout'])
        self.embedding_dropout = nn.Dropout(config['dropout'] * 0.5)

        # Positional encoding with proper initialization
        self.pos_encoding = nn.Parameter(torch.randn(1, self.n_patches, config['d_model']) * 0.02)

        # Enhanced Transformer Encoder with progressive dropout
        encoder_layers = []
        for i in range(config['n_layers']):
            # Gradually increase dropout in deeper layers
            layer_dropout = min(config['dropout'] * (1 + 0.1 * i), 0.4)
            encoder_layer = nn.TransformerEncoderLayer(
                d_model=config['d_model'], 
                nhead=config['n_heads'],
                dim_feedforward=config['d_model'] * 4, 
                dropout=layer_dropout,
                batch_first=True, 
                activation='gelu'
            )
            encoder_layers.append(encoder_layer)
        
        self.transformer_layers = nn.ModuleList(encoder_layers)
        
        # Enhanced Classification Head
        self.pre_head_norm = nn.LayerNorm(config['d_model'])
        self.head = nn.Sequential(
            nn.Dropout(config['dropout']),
            nn.Linear(config['d_model'], config['d_model'] // 2),
            nn.GELU(),
            nn.LayerNorm(config['d_model'] // 2),
            nn.Dropout(config['dropout'] * 0.8),
            nn.Linear(config['d_model'] // 2, config['d_model'] // 4),
            nn.GELU(),
            nn.Dropout(config['dropout'] * 0.6),
            nn.Linear(config['d_model'] // 4, 2)
        )

        # Enhanced Loss Function
        if config.get('use_focal_loss', True):
            self.criterion = EnhancedFocalLoss(
                alpha=class_weights, 
                gamma=config.get('focal_loss_gamma', 1.5),
                label_smoothing=config.get('label_smoothing', 0.1)
            )
        else:
            self.criterion = nn.CrossEntropyLoss(
                weight=class_weights, 
                label_smoothing=config.get('label_smoothing', 0.1)
            )
        
        self.validation_step_outputs = []

    def forward(self, x):
        batch_size = x.shape[0]
        
        # Patching with enhanced dropout
        x = x.view(batch_size, self.n_patches, 
                  self.hparams.config['patch_size'] * self.hparams.n_features)
        
        # Embedding with dropout
        x = self.patch_embedding(x)
        x = self.embedding_dropout(x)
        
        # Add positional encoding
        x = x + self.pos_encoding
        x = self.patch_dropout(x)
        
        # Enhanced transformer with optional stochastic depth
        for i, layer in enumerate(self.transformer_layers):
            # Optional: Skip layers with small probability during training
            if self.training and torch.rand(1) < 0.05 * i / len(self.transformer_layers):
                continue
            x = layer(x)
        
        # Global average pooling for better generalization
        x = x.mean(dim=1)
        
        # Pre-head normalization
        x = self.pre_head_norm(x)
        
        # Classification head
        return self.head(x)

    def training_step(self, batch, batch_idx):
        x, y = batch
        logits = self(x)
        loss = self.criterion(logits, y)
        
        # Enhanced L2 regularization with layer-specific weights
        l2_reg = torch.tensor(0., device=self.device)
        for name, param in self.named_parameters():
            if 'weight' in name:
                if 'transformer' in name:
                    l2_reg += 0.0005 * torch.norm(param)
                elif 'head' in name:
                    l2_reg += 0.001 * torch.norm(param)
                else:
                    l2_reg += 0.0001 * torch.norm(param)
        
        total_loss = loss + l2_reg
        
        # Enhanced logging
        self.log('train_loss', total_loss, on_step=True, on_epoch=True, prog_bar=True)
        self.log('train_ce_loss', loss, on_step=False, on_epoch=True)
        self.log('train_l2_reg', l2_reg, on_step=False, on_epoch=True)
        
        return total_loss

    def validation_step(self, batch, batch_idx):
        x, y = batch
        logits = self(x)
        loss = self.criterion(logits, y)
        preds = torch.argmax(logits, dim=1)
        
        self.validation_step_outputs.append({
            'loss': loss, 
            'preds': preds, 
            'targets': y,
            'logits': logits
        })
        return loss

    def on_validation_epoch_end(self):
        # Calculate metrics
        avg_loss = torch.stack([x['loss'] for x in self.validation_step_outputs]).mean()
        all_preds = torch.cat([x['preds'] for x in self.validation_step_outputs])
        all_targets = torch.cat([x['targets'] for x in self.validation_step_outputs])
        all_logits = torch.cat([x['logits'] for x in self.validation_step_outputs])
        
        # Calculate performance metrics
        val_f1 = f1_score(all_targets.cpu(), all_preds.cpu(), zero_division=0)
        val_precision = precision_score(all_targets.cpu(), all_preds.cpu(), zero_division=0)
        val_recall = recall_score(all_targets.cpu(), all_preds.cpu(), zero_division=0)
        
        # Calculate confidence metrics
        probs = torch.softmax(all_logits, dim=1)
        confidence = probs.max(dim=1)[0].mean()
        
        # Enhanced logging
        self.log_dict({
            'val_loss': avg_loss,
            'val_f1': val_f1,
            'val_precision': val_precision,
            'val_recall': val_recall,
            'val_confidence': confidence
        }, prog_bar=True, logger=True)
        
        self.validation_step_outputs.clear()

    def configure_optimizers(self):
        # Enhanced optimizer with differential learning rates
        param_groups = [
            {
                'params': [p for n, p in self.named_parameters() if 'transformer' in n],
                'lr': self.hparams.config['learning_rate'] * 0.5,
                'weight_decay': 0.01
            },
            {
                'params': [p for n, p in self.named_parameters() if 'head' in n],
                'lr': self.hparams.config['learning_rate'] * 0.8,
                'weight_decay': 0.02
            },
            {
                'params': [p for n, p in self.named_parameters() 
                          if 'embedding' in n or 'pos_encoding' in n],
                'lr': self.hparams.config['learning_rate'] * 0.3,
                'weight_decay': 0.005
            }
        ]
        
        optimizer = optim.AdamW(param_groups)
        
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, 
            mode='max', 
            factor=0.5, 
            patience=self.hparams.config.get('lr_scheduler_patience', 2),
            verbose=True,
            min_lr=1e-6
        )
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler, 
                'monitor': 'val_f1',
                'frequency': 1
            }
        }

print("✅ Enhanced PatchTST model with advanced regularization created")

# 📊 Create CPU-Optimized Datasets and DataLoaders
print("📊 Creating CPU-optimized datasets...")

# Create datasets
train_dataset = CPUOptimizedCMEDataset(train_df, feature_columns, 'event_label', CONFIG['sequence_length'])
val_dataset = CPUOptimizedCMEDataset(val_df, feature_columns, 'event_label', CONFIG['sequence_length'])
test_dataset = CPUOptimizedCMEDataset(test_df, feature_columns, 'event_label', CONFIG['sequence_length'])

print(f"✅ Datasets created:")
print(f"   Train: {len(train_dataset):,} sequences")
print(f"   Validation: {len(val_dataset):,} sequences")
print(f"   Test: {len(test_dataset):,} sequences")

# Calculate class weights for balanced training
train_targets = []
print("🔍 Calculating class weights...")

# Sample a subset for class weight calculation (faster)
sample_size = min(10000, len(train_dataset))
sample_indices = np.random.choice(len(train_dataset), sample_size, replace=False)

for idx in sample_indices:
    _, target = train_dataset[idx]
    train_targets.append(target.item())

class_counts = np.bincount(train_targets)

# Handle case where one class might be missing
if len(class_counts) == 1:
    print("⚠️ Only one class found in training data. Adding minimal representation of missing class.")
    # Extend class_counts to include both classes
    if class_counts[0] > 0:  # Only class 0 exists
        class_counts = np.array([class_counts[0], 1])  # Add 1 sample for class 1
    else:  # Only class 1 exists (unlikely)
        class_counts = np.array([1, class_counts[0]])  # Add 1 sample for class 0

# Ensure we have exactly 2 classes
if len(class_counts) < 2:
    class_counts = np.array([max(1, len(train_targets) - 1), 1])

class_weights = torch.FloatTensor(len(class_counts) / class_counts)

print(f"📊 Class distribution (sampled):")
print(f"   Class 0 (Normal): {class_counts[0]:,} ({class_counts[0]/len(train_targets)*100:.1f}%)")
print(f"   Class 1 (CME): {class_counts[1]:,} ({class_counts[1]/len(train_targets)*100:.1f}%)")
print(f"   Class weights: {class_weights.tolist()}")

# Create CPU-optimized DataLoaders
def create_optimized_dataloaders():
    """Create DataLoaders with CPU optimization"""
    
    # Determine optimal number of workers
    optimal_workers = CONFIG['num_workers']
    use_pin_memory = CONFIG['pin_memory']
    
    print(f"🔧 DataLoader optimization:")
    print(f"   Workers: {optimal_workers}")
    print(f"   Pin memory: {use_pin_memory}")
    print(f"   Batch size: {CONFIG['batch_size']}")
    print(f"   Persistent workers: {CONFIG['persistent_workers']}")
    
    # Common DataLoader settings
    dataloader_kwargs = {
        'batch_size': CONFIG['batch_size'],
        'num_workers': optimal_workers,
        'pin_memory': use_pin_memory,
        'persistent_workers': CONFIG['persistent_workers'] if optimal_workers > 0 else False,
        'prefetch_factor': 2 if optimal_workers > 0 else 2
    }
    
    # Create DataLoaders
    train_loader = DataLoader(train_dataset, shuffle=True, **dataloader_kwargs)
    val_loader = DataLoader(val_dataset, shuffle=False, **dataloader_kwargs)
    test_loader = DataLoader(test_dataset, shuffle=False, **dataloader_kwargs)
    
    return train_loader, val_loader, test_loader

train_loader, val_loader, test_loader = create_optimized_dataloaders()

print("✅ CPU-optimized DataLoaders created")

# Test DataLoader performance
print("\n⏱️ Testing DataLoader performance...")
start_time = time.time()

for i, batch in enumerate(train_loader):
    if i >= 5:  # Test 5 batches
        break
    x, y = batch
    # Just access the data to test loading speed

end_time = time.time()
print(f"⏱️ Time for 5 batches: {end_time - start_time:.2f} seconds")
print(f"📊 Batch shape: {x.shape}, Target shape: {y.shape}")

print("\n📊 Memory usage after dataset creation:")
monitor_system_resources()

# 🚀 Model Training with CPU Optimization
print("🚀 Setting up enhanced training with CPU optimization...")

# Create enhanced model
model = EnhancedPatchTSTModel(
    config=CONFIG,
    n_features=len(feature_columns),
    class_weights=class_weights
)

print(f"🤖 Model created with {sum(p.numel() for p in model.parameters() if p.requires_grad):,} parameters")

# Enhanced callbacks
checkpoint_callback = ModelCheckpoint(
    monitor='val_f1',
    mode='max',
    dirpath='./checkpoints',
    filename='celest-ai-cpu-optimized-{epoch:02d}-{val_f1:.3f}',
    save_top_k=3,
    save_last=True,
    verbose=True
)

early_stopping = EarlyStopping(
    monitor='val_f1',
    patience=CONFIG['early_stopping_patience'],
    mode='max',
    verbose=True
)

lr_monitor = LearningRateMonitor(logging_interval='epoch')

# CPU-optimized trainer configuration
def get_trainer_config():
    """Get trainer configuration optimized for current hardware"""
    
    is_gpu_available = system_info['is_gpu_available']
    
    if not is_gpu_available:
        print("🖥️ Configuring for CPU-only training...")
        trainer_config = {
            'accelerator': 'cpu',
            'devices': 1,
            'precision': 32,  # Use 32-bit for CPU
            'accumulate_grad_batches': 4,  # Higher accumulation for CPU stability
            'enable_progress_bar': True,
            'enable_model_summary': True,
            'num_sanity_val_steps': 2,  # Reduce sanity checks
        }
        precision_info = "32-bit (CPU optimized)"
        accumulation_info = "4 batches (CPU optimized)"
    else:
        print("🚀 Configuring for GPU training...")
        trainer_config = {
            'accelerator': 'gpu',
            'devices': 1,
            'precision': 16,  # Mixed precision for GPU
            'accumulate_grad_batches': 2,
            'enable_progress_bar': True,
            'enable_model_summary': True,
        }
        precision_info = "16-bit mixed precision"
        accumulation_info = "2 batches"
    
    print(f"✅ Trainer configuration:")
    print(f"   Hardware: {'GPU' if is_gpu_available else 'CPU'}")
    print(f"   Precision: {precision_info}")
    print(f"   Gradient accumulation: {accumulation_info}")
    print(f"   CPU cores available: {system_info['cpu_cores']}")
    
    return trainer_config

trainer_config = get_trainer_config()

# Create optimized trainer
trainer = pl.Trainer(
    max_epochs=CONFIG['max_epochs'],
    callbacks=[checkpoint_callback, early_stopping, lr_monitor],
    log_every_n_steps=50,
    gradient_clip_val=CONFIG['gradient_clip_val'],
    val_check_interval=0.5,  # Check validation twice per epoch
    deterministic=True,
    **trainer_config
)

print("\n📊 System status before training:")
monitor_system_resources()

print("\n🚀 Starting CPU-optimized enhanced training...")
print("\n🎯 Target Performance Metrics:")
print("   F1 Score: ≥ 0.82")
print("   Precision: ≥ 0.78")
print("   Recall: ≥ 0.86")
print("\n📊 Monitor these indicators:")
print("   ✅ Val loss should decrease steadily")
print("   ✅ F1 score should stay > 0.82")
print("   ✅ Precision should recover to > 0.78")
print("   ✅ Train/Val loss gap should be < 2x")

# Start training
trainer.fit(model, train_loader, val_loader)

# 📊 Model Evaluation and Performance Analysis
print("📊 Evaluating model performance...")

# Test the model
test_results = trainer.test(model, test_loader)

print("\n🧪 Detailed evaluation on test set...")

# Get predictions on test set
model.eval()
all_preds = []
all_targets = []
all_probs = []

with torch.no_grad():
    for batch in test_loader:
        x, y = batch
        logits = model(x)
        probs = torch.softmax(logits, dim=1)
        preds = torch.argmax(logits, dim=1)
        
        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(y.cpu().numpy())
        all_probs.extend(probs.cpu().numpy())

# Calculate comprehensive metrics
test_f1 = f1_score(all_targets, all_preds)
test_precision = precision_score(all_targets, all_preds)
test_recall = recall_score(all_targets, all_preds)

print(f"\n🎯 Final Test Results:")
print(f"   F1 Score: {test_f1:.4f} {'✅' if test_f1 >= 0.82 else '❌'} (Target: ≥0.82)")
print(f"   Precision: {test_precision:.4f} {'✅' if test_precision >= 0.78 else '❌'} (Target: ≥0.78)")
print(f"   Recall: {test_recall:.4f} {'✅' if test_recall >= 0.86 else '❌'} (Target: ≥0.86)")

# Detailed classification report
print("\n📋 Detailed Classification Report:")
print(classification_report(all_targets, all_preds, target_names=['Normal', 'CME']))

# Confusion Matrix
cm = confusion_matrix(all_targets, all_preds)
print("\n📊 Confusion Matrix:")
print(f"   True Negatives: {cm[0,0]:,}")
print(f"   False Positives: {cm[0,1]:,}")
print(f"   False Negatives: {cm[1,0]:,}")
print(f"   True Positives: {cm[1,1]:,}")

# Calculate operational metrics
false_alarm_rate = cm[0,1] / (cm[0,0] + cm[0,1]) * 100
miss_rate = cm[1,0] / (cm[1,0] + cm[1,1]) * 100

print(f"\n🚨 Operational Metrics:")
print(f"   False Alarm Rate: {false_alarm_rate:.2f}%")
print(f"   Miss Rate: {miss_rate:.2f}%")
print(f"   Detection Rate: {100-miss_rate:.2f}%")

# Performance summary
print(f"\n🎉 CELEST AI Performance Summary:")
print(f"   🎯 Achieves F1 = {test_f1:.3f} {'(EXCEEDS TARGET!)' if test_f1 >= 0.82 else '(Below target)'}")
print(f"   🎯 Precision = {test_precision:.3f} {'(EXCEEDS TARGET!)' if test_precision >= 0.78 else '(Below target)'}")
print(f"   🎯 Recall = {test_recall:.3f} {'(EXCEEDS TARGET!)' if test_recall >= 0.86 else '(Below target)'}")
print(f"   🚀 Detects {100-miss_rate:.1f}% of dangerous CME events")
print(f"   ⚡ False alarm rate of only {false_alarm_rate:.1f}%")

print("\n📊 Final system status:")
monitor_system_resources()
optimize_memory_usage()